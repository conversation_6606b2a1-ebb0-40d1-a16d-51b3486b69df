'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface EidSalePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

// 2-Step account pricing with Eid discounts
const eidPricingOffers = [
  {
    size: '$1,000',
    originalPrice: 15,
    salePrice: 6,
    savings: 9,
    discount: '60%'
  },
  {
    size: '$3,000',
    originalPrice: 25,
    salePrice: 10,
    savings: 15,
    discount: '60%'
  },
  {
    size: '$5,000',
    originalPrice: 45,
    salePrice: 18,
    savings: 27,
    discount: '60%'
  },
  {
    size: '$10,000',
    originalPrice: 70,
    salePrice: 28,
    savings: 42,
    discount: '60%'
  },
  {
    size: '$25,000',
    originalPrice: 138,
    salePrice: 55,
    savings: 83,
    discount: '60%'
  },
  {
    size: '$50,000',
    originalPrice: 125,
    salePrice: 50,
    savings: 75,
    discount: '60%'
  },
  {
    size: '$100,000',
    originalPrice: 188,
    salePrice: 75,
    savings: 113,
    discount: '60%'
  },
  {
    size: '$200,000',
    originalPrice: 313,
    salePrice: 125,
    savings: 188,
    discount: '60%'
  }
];

const EidSalePopup: React.FC<EidSalePopupProps> = ({ isOpen, onClose }) => {
  const [selectedOffer, setSelectedOffer] = useState(eidPricingOffers[2]); // Default to $5,000
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Countdown timer for Eid sale (set to 7 days from now)
  useEffect(() => {
    const saleEndDate = new Date();
    saleEndDate.setDate(saleEndDate.getDate() + 7); // 7 days from now

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = saleEndDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm animate-fadeIn"
          onClick={onClose}
        />

        {/* Modal content */}
        <div className="inline-block align-bottom bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 rounded-lg text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full border border-slate-700/50 animate-slideUp backdrop-blur-sm">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-3 right-3 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-slate-800/80 hover:bg-slate-700/80 text-slate-400 hover:text-white focus:outline-none transition-all duration-200 border border-slate-600/30 hover:border-slate-500/50"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Header with Eid theme */}
          <div className="relative bg-gradient-to-r from-slate-800/50 to-slate-700/50 px-2 py-2 border-b border-slate-600/30">
            <div className="text-center">
              <div className="inline-block mb-1 px-2 py-0.5 bg-gradient-to-r from-emerald-600/20 to-teal-600/20 rounded-full border border-emerald-500/40 backdrop-blur-sm">
                <span className="text-emerald-300 text-xs font-medium tracking-wide">🌙 EID SPECIAL OFFER 🌙</span>
              </div>
              <h2 className="text-lg font-bold text-white mb-1 tracking-tight">
                60% OFF Premium Sale
              </h2>
              <p className="text-xs text-slate-300 font-medium">
                All 2-Step Challenge Accounts
              </p>
            </div>
          </div>

          {/* Countdown Timer */}
          <div className="px-2 py-1 bg-gradient-to-r from-slate-800/40 to-slate-700/40 border-b border-slate-600/30">
            <div className="text-center">
              <p className="text-slate-300 text-xs font-medium tracking-wider">
                <span className="text-amber-400">⏰</span> EXPIRES IN: {timeLeft.days}d {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s
              </p>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-2 py-2">
            {/* Account Size Selection */}
            <div className="mb-2">
              <h3 className="text-xs font-semibold text-slate-200 mb-1 text-center tracking-wide uppercase">
                Select Account Size
              </h3>
              <div className="grid grid-cols-4 gap-1">
                {eidPricingOffers.map((offer, index) => (
                  <div
                    key={index}
                    onClick={() => setSelectedOffer(offer)}
                    className={`relative cursor-pointer p-1 text-center rounded transition-all duration-300 border backdrop-blur-sm ${
                      selectedOffer.size === offer.size
                        ? 'bg-gradient-to-b from-emerald-600/30 to-teal-600/30 text-white border-emerald-400/60 shadow-lg shadow-emerald-500/20 ring-1 ring-emerald-400/30'
                        : 'bg-slate-800/40 hover:bg-slate-700/50 text-slate-300 border-slate-600/40 hover:border-slate-500/60'
                    }`}
                  >
                    <div className="font-semibold text-xs tracking-wide">{offer.size.replace('$', '').replace(',', '')}K</div>
                    <div className="text-xs font-bold text-emerald-300">
                      ${offer.salePrice}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Offer Details */}
            <div className="bg-gradient-to-r from-slate-800/30 to-slate-700/30 rounded p-2 border border-slate-600/30 mb-2 backdrop-blur-sm">
              <div className="text-center">
                <h4 className="text-sm font-bold text-white mb-1 tracking-tight">
                  {selectedOffer.size} Professional Challenge
                </h4>
                <div className="flex justify-center items-center space-x-2 mb-1">
                  <div className="text-xs text-slate-400 line-through font-medium">${selectedOffer.originalPrice}</div>
                  <div className="text-lg text-emerald-400 font-light">→</div>
                  <div className="text-lg font-bold text-emerald-300">${selectedOffer.salePrice}</div>
                </div>
                <div className="text-amber-300 font-semibold text-xs tracking-wide">
                  💎 SAVE ${selectedOffer.savings}
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="bg-slate-800/30 rounded p-1.5 border border-slate-600/30 mb-2 backdrop-blur-sm">
              <div className="text-center">
                <div className="text-slate-200 font-medium text-xs mb-1 tracking-wide">✨ 2-Phase Evaluation • 90% Profit Share • $200K Maximum • Professional Tools</div>
                <div className="text-emerald-300 font-semibold text-xs tracking-wide">🎁 60% DISCOUNT • Limited Time • Instant Activation</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex gap-1 justify-center">
              <Link
                href={`/place-order?type=twoStep&size=${selectedOffer.size.replace('$', '').replace(',', '')}&eid_sale=true`}
                className="flex-1 px-3 py-1.5 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-500 hover:to-teal-500 text-white font-bold rounded shadow-lg shadow-emerald-500/30 hover:shadow-emerald-500/50 transition-all duration-300 text-center border border-emerald-400/30 text-xs tracking-wide backdrop-blur-sm"
                onClick={onClose}
              >
                🚀 CLAIM ${selectedOffer.salePrice}
              </Link>
              <button
                onClick={onClose}
                className="px-2 py-1.5 bg-slate-700/50 hover:bg-slate-600/60 text-slate-300 hover:text-white font-semibold rounded border border-slate-600/50 hover:border-slate-500/60 transition-all duration-300 text-xs backdrop-blur-sm"
              >
                Later
              </button>
            </div>

            {/* Footer */}
            <div className="mt-1 text-center">
              <div className="flex justify-center items-center gap-2 text-slate-400 text-xs mb-1 font-medium">
                <span>🔒 Bank-Level Security</span>
                <span>⚡ Instant Activation</span>
                <span>🎯 Proven Results</span>
              </div>
              <button
                onClick={() => {
                  localStorage.setItem('eid-popup-dismissed', 'true');
                  onClose();
                }}
                className="text-slate-400 hover:text-slate-300 text-xs underline transition-colors font-medium"
              >
                Don't show this offer again
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EidSalePopup;
