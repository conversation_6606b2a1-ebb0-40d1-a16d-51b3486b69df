'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface EidSalePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

// 2-Step account pricing with Eid discounts
const eidPricingOffers = [
  {
    size: '$1,000',
    originalPrice: 15,
    salePrice: 6,
    savings: 9,
    discount: '60%'
  },
  {
    size: '$3,000',
    originalPrice: 25,
    salePrice: 10,
    savings: 15,
    discount: '60%'
  },
  {
    size: '$5,000',
    originalPrice: 45,
    salePrice: 18,
    savings: 27,
    discount: '60%'
  },
  {
    size: '$10,000',
    originalPrice: 70,
    salePrice: 28,
    savings: 42,
    discount: '60%'
  },
  {
    size: '$25,000',
    originalPrice: 138,
    salePrice: 55,
    savings: 83,
    discount: '60%'
  },
  {
    size: '$50,000',
    originalPrice: 125,
    salePrice: 50,
    savings: 75,
    discount: '60%'
  },
  {
    size: '$100,000',
    originalPrice: 188,
    salePrice: 75,
    savings: 113,
    discount: '60%'
  },
  {
    size: '$200,000',
    originalPrice: 313,
    salePrice: 125,
    savings: 188,
    discount: '60%'
  }
];

const EidSalePopup: React.FC<EidSalePopupProps> = ({ isOpen, onClose }) => {
  const [selectedOffer, setSelectedOffer] = useState(eidPricingOffers[2]); // Default to $5,000
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Countdown timer for Eid sale (set to 7 days from now)
  useEffect(() => {
    const saleEndDate = new Date();
    saleEndDate.setDate(saleEndDate.getDate() + 7); // 7 days from now

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = saleEndDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm animate-fadeIn"
          onClick={onClose}
        />

        {/* Modal content */}
        <div className="inline-block align-bottom bg-gradient-to-br from-[#0F1A2E] to-[#070F1B] rounded-lg text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full border border-teal-500/20 animate-slideUp">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 text-gray-400 hover:text-white focus:outline-none transition-colors"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Header with Eid theme */}
          <div className="relative bg-gradient-to-r from-teal-600/20 to-teal-500/20 px-2 py-2 border-b border-teal-500/20">
            <div className="text-center">
              <div className="inline-block mb-1 px-2 py-0.5 bg-gradient-to-r from-teal-500/20 to-teal-400/20 rounded-full border border-teal-400/30">
                <span className="text-teal-300 text-xs font-semibold">🌙 EID SPECIAL 🌙</span>
              </div>
              <h2 className="text-lg font-bold text-white mb-1">
                60% OFF Eid Sale
              </h2>
              <p className="text-xs text-teal-300 font-semibold">
                All 2-Step Challenge Accounts
              </p>
            </div>
          </div>

          {/* Countdown Timer */}
          <div className="px-2 py-1 bg-gradient-to-r from-teal-900/30 to-teal-800/30 border-b border-teal-500/20">
            <div className="text-center">
              <p className="text-gray-300 text-xs mb-1">⏰ {timeLeft.days}d {timeLeft.hours}h {timeLeft.minutes}m {timeLeft.seconds}s</p>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-2 py-2">
            {/* Account Size Selection */}
            <div className="mb-2">
              <h3 className="text-xs font-semibold text-white mb-1 text-center">
                Choose Account Size
              </h3>
              <div className="grid grid-cols-4 gap-1">
                {eidPricingOffers.map((offer, index) => (
                  <div
                    key={index}
                    onClick={() => setSelectedOffer(offer)}
                    className={`relative cursor-pointer p-1 text-center rounded transition-all duration-300 border ${
                      selectedOffer.size === offer.size
                        ? 'bg-gradient-to-b from-teal-600/80 to-teal-700/80 text-white border-teal-400/50 shadow-lg shadow-teal-500/20 transform scale-105'
                        : 'bg-dark/60 hover:bg-dark-light/60 text-gray-300 border-gray-700/50 hover:border-gray-600/50'
                    }`}
                  >
                    <div className="font-bold text-xs">{offer.size.replace('$', '').replace(',', '')}K</div>
                    <div className="text-xs font-semibold text-teal-300">
                      ${offer.salePrice}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Offer Details */}
            <div className="bg-gradient-to-r from-teal-900/20 to-teal-800/20 rounded p-2 border border-teal-500/20 mb-2">
              <div className="text-center">
                <h4 className="text-sm font-bold text-white mb-1">
                  {selectedOffer.size} Challenge
                </h4>
                <div className="flex justify-center items-center space-x-2 mb-1">
                  <div className="text-xs text-gray-400 line-through">${selectedOffer.originalPrice}</div>
                  <div className="text-lg text-teal-400">→</div>
                  <div className="text-lg font-bold text-teal-300">${selectedOffer.salePrice}</div>
                </div>
                <div className="text-green-300 font-semibold text-xs">
                  💰 Save ${selectedOffer.savings}
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="bg-dark/40 rounded p-1.5 border border-gray-700/50 mb-2">
              <div className="text-center">
                <div className="text-white font-semibold text-xs mb-1">✨ 2-Phase • 90% Profit • $200K Max • Pro Tools</div>
                <div className="text-teal-300 font-semibold text-xs">🎁 60% OFF • Limited Time • Instant Access</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex gap-1 justify-center">
              <Link
                href={`/place-order?type=twoStep&size=${selectedOffer.size.replace('$', '').replace(',', '')}&eid_sale=true`}
                className="flex-1 px-3 py-1.5 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold rounded shadow-lg shadow-teal-500/30 hover:shadow-teal-500/50 transition-all duration-300 text-center border border-teal-400/20 text-xs"
                onClick={onClose}
              >
                🚀 Claim ${selectedOffer.salePrice}
              </Link>
              <button
                onClick={onClose}
                className="px-2 py-1.5 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white font-semibold rounded border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300 text-xs"
              >
                Later
              </button>
            </div>

            {/* Footer */}
            <div className="mt-1 text-center">
              <div className="flex justify-center items-center gap-2 text-gray-400 text-xs mb-1">
                <span>🔒 Secure</span>
                <span>⚡ Instant</span>
                <span>🎯 Proven</span>
              </div>
              <button
                onClick={() => {
                  localStorage.setItem('eid-popup-dismissed', 'true');
                  onClose();
                }}
                className="text-gray-400 hover:text-gray-300 text-xs underline transition-colors"
              >
                Don't show again
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EidSalePopup;
