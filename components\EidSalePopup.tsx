'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface EidSalePopupProps {
  isOpen: boolean;
  onClose: () => void;
}

// 2-Step account pricing with Eid discounts
const eidPricingOffers = [
  {
    size: '$1,000',
    originalPrice: 15,
    salePrice: 6,
    savings: 9,
    discount: '60%'
  },
  {
    size: '$3,000',
    originalPrice: 25,
    salePrice: 10,
    savings: 15,
    discount: '60%'
  },
  {
    size: '$5,000',
    originalPrice: 45,
    salePrice: 18,
    savings: 27,
    discount: '60%'
  },
  {
    size: '$10,000',
    originalPrice: 70,
    salePrice: 28,
    savings: 42,
    discount: '60%'
  },
  {
    size: '$25,000',
    originalPrice: 138,
    salePrice: 55,
    savings: 83,
    discount: '60%'
  },
  {
    size: '$50,000',
    originalPrice: 125,
    salePrice: 50,
    savings: 75,
    discount: '60%'
  },
  {
    size: '$100,000',
    originalPrice: 188,
    salePrice: 75,
    savings: 113,
    discount: '60%'
  },
  {
    size: '$200,000',
    originalPrice: 313,
    salePrice: 125,
    savings: 188,
    discount: '60%'
  }
];

const EidSalePopup: React.FC<EidSalePopupProps> = ({ isOpen, onClose }) => {
  const [selectedOffer, setSelectedOffer] = useState(eidPricingOffers[2]); // Default to $5,000
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  // Countdown timer for Eid sale (set to 7 days from now)
  useEffect(() => {
    const saleEndDate = new Date();
    saleEndDate.setDate(saleEndDate.getDate() + 7); // 7 days from now

    const timer = setInterval(() => {
      const now = new Date().getTime();
      const distance = saleEndDate.getTime() - now;

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000)
        });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto animate-fadeIn">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity bg-black bg-opacity-75 backdrop-blur-sm animate-fadeIn"
          onClick={onClose}
        />

        {/* Modal content */}
        <div className="inline-block align-bottom bg-gradient-to-br from-[#0F1A2E] to-[#070F1B] rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full border border-teal-500/20 animate-slideUp">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 text-gray-400 hover:text-white focus:outline-none transition-colors"
          >
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Header with Eid theme */}
          <div className="relative bg-gradient-to-r from-teal-600/20 to-teal-500/20 px-3 py-4 border-b border-teal-500/20">
            {/* Decorative elements */}
            <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
              <div className="absolute top-2 left-2 w-4 h-4 text-teal-400/30 animate-sparkle">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
                </svg>
              </div>
              <div className="absolute top-2 right-2 w-3 h-3 text-teal-400/20 animate-sparkle" style={{ animationDelay: '1s' }}>
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z"/>
                </svg>
              </div>
            </div>

            <div className="relative z-10 text-center">
              <div className="inline-block mb-2 px-3 py-1 bg-gradient-to-r from-teal-500/20 to-teal-400/20 rounded-full border border-teal-400/30">
                <span className="text-teal-300 text-xs font-semibold">🌙 EID MUBARAK SPECIAL 🌙</span>
              </div>
              <h2 className="text-xl md:text-2xl font-bold text-white mb-1">
                Exclusive Eid Sale
              </h2>
              <p className="text-base text-teal-300 font-semibold">
                60% OFF on All 2-Step Challenge Accounts
              </p>
              <p className="text-gray-300 mt-1 text-xs">
                Celebrate Eid with our biggest discount of the year!
              </p>
            </div>
          </div>

          {/* Countdown Timer */}
          <div className="px-3 py-2 bg-gradient-to-r from-teal-900/30 to-teal-800/30 border-b border-teal-500/20">
            <div className="text-center">
              <p className="text-gray-300 text-xs mb-1">⏰ Offer Ends In:</p>
              <div className="flex justify-center space-x-2">
                <div className="bg-teal-600/20 rounded px-1.5 py-1 border border-teal-500/30 min-w-[40px]">
                  <div className="text-sm font-bold text-white">{timeLeft.days}</div>
                  <div className="text-xs text-gray-300">Days</div>
                </div>
                <div className="bg-teal-600/20 rounded px-1.5 py-1 border border-teal-500/30 min-w-[40px]">
                  <div className="text-sm font-bold text-white">{timeLeft.hours}</div>
                  <div className="text-xs text-gray-300">Hrs</div>
                </div>
                <div className="bg-teal-600/20 rounded px-1.5 py-1 border border-teal-500/30 min-w-[40px]">
                  <div className="text-sm font-bold text-white">{timeLeft.minutes}</div>
                  <div className="text-xs text-gray-300">Min</div>
                </div>
                <div className="bg-teal-600/20 rounded px-1.5 py-1 border border-teal-500/30 min-w-[40px]">
                  <div className="text-sm font-bold text-white">{timeLeft.seconds}</div>
                  <div className="text-xs text-gray-300">Sec</div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-3 py-3">
            {/* Account Size Selection */}
            <div className="mb-3">
              <h3 className="text-sm font-semibold text-white mb-2 text-center">
                Choose Your 2-Step Challenge Account Size
              </h3>
              <div className="grid grid-cols-4 gap-1.5">
                {eidPricingOffers.map((offer, index) => (
                  <div
                    key={index}
                    onClick={() => setSelectedOffer(offer)}
                    className={`relative cursor-pointer p-1.5 text-center rounded transition-all duration-300 border ${
                      selectedOffer.size === offer.size
                        ? 'bg-gradient-to-b from-teal-600/80 to-teal-700/80 text-white border-teal-400/50 shadow-lg shadow-teal-500/20 transform scale-105'
                        : 'bg-dark/60 hover:bg-dark-light/60 text-gray-300 border-gray-700/50 hover:border-gray-600/50'
                    }`}
                  >
                    {/* Discount badge */}
                    <div className="absolute -top-1 -right-1 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black text-xs font-bold py-0.5 px-1 rounded-full transform rotate-12 shadow-lg">
                      {offer.discount}
                    </div>
                    <div className="font-bold text-xs">{offer.size.replace('$', '').replace(',', '')}K</div>
                    <div className="text-xs text-gray-400">
                      <span className="line-through">${offer.originalPrice}</span>
                    </div>
                    <div className="text-xs font-semibold text-teal-300">
                      ${offer.salePrice}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Selected Offer Details */}
            <div className="bg-gradient-to-r from-teal-900/20 to-teal-800/20 rounded p-3 border border-teal-500/20 mb-3">
              <div className="text-center">
                <h4 className="text-base font-bold text-white mb-2">
                  {selectedOffer.size} 2-Step Challenge
                </h4>
                <div className="flex justify-center items-center space-x-2 mb-2">
                  <div className="text-center">
                    <div className="text-gray-400 text-xs">Was</div>
                    <div className="text-sm text-gray-400 line-through">${selectedOffer.originalPrice}</div>
                  </div>
                  <div className="text-xl text-teal-400">→</div>
                  <div className="text-center">
                    <div className="text-teal-300 text-xs">Now</div>
                    <div className="text-xl font-bold text-teal-300">${selectedOffer.salePrice}</div>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-green-600/20 to-green-500/20 rounded p-1.5 border border-green-500/30">
                  <div className="text-green-300 font-semibold text-xs">
                    💰 Save ${selectedOffer.savings} ({selectedOffer.discount} OFF)
                  </div>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div className="bg-dark/40 rounded p-2 border border-gray-700/50">
                <h5 className="text-white font-semibold mb-1 text-xs">✨ Features</h5>
                <ul className="text-gray-300 text-xs space-y-0.5">
                  <li>• Two-phase evaluation</li>
                  <li>• Up to 90% profit sharing</li>
                  <li>• Max capital: $200K</li>
                  <li>• Pro trading tools</li>
                </ul>
              </div>
              <div className="bg-dark/40 rounded p-2 border border-gray-700/50">
                <h5 className="text-white font-semibold mb-1 text-xs">🎁 Eid Benefits</h5>
                <ul className="text-gray-300 text-xs space-y-0.5">
                  <li>• 60% discount</li>
                  <li>• Limited time offer</li>
                  <li>• Instant activation</li>
                  <li>• Priority support</li>
                </ul>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex gap-2 justify-center">
              <Link
                href={`/place-order?type=twoStep&size=${selectedOffer.size.replace('$', '').replace(',', '')}&eid_sale=true`}
                className="flex-1 px-4 py-2 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold rounded shadow-lg shadow-teal-500/30 hover:shadow-teal-500/50 transition-all duration-300 transform hover:translate-y-[-1px] text-center border border-teal-400/20 text-xs"
                onClick={onClose}
              >
                🚀 Claim Offer - ${selectedOffer.salePrice}
              </Link>
              <button
                onClick={onClose}
                className="px-3 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white font-semibold rounded border border-gray-600/50 hover:border-gray-500/50 transition-all duration-300 text-xs"
              >
                Later
              </button>
            </div>

            {/* Don't show again option */}
            <div className="mt-2 text-center">
              <button
                onClick={() => {
                  localStorage.setItem('eid-popup-dismissed', 'true');
                  onClose();
                }}
                className="text-gray-400 hover:text-gray-300 text-xs underline transition-colors"
              >
                Don't show again
              </button>
            </div>

            {/* Trust indicators */}
            <div className="mt-2 text-center">
              <div className="flex justify-center items-center gap-3 text-gray-400 text-xs">
                <div className="flex items-center space-x-1">
                  <span>🔒</span>
                  <span>Secure</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span>⚡</span>
                  <span>Instant</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span>🎯</span>
                  <span>Proven</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EidSalePopup;
