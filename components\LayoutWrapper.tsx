'use client';

import { usePathname } from 'next/navigation';
import Navbar from './Navbar';
import Footer from './Footer';

export default function LayoutWrapper({ 
  children 
}: { 
  children: React.ReactNode 
}) {
  const pathname = usePathname();
  const isDashboard = pathname?.startsWith('/dashboard');
  const isAdmin = pathname?.startsWith('/admin');
  
  return (
    <div className="relative z-10 flex flex-col min-h-screen w-full p-0 m-0">
      {!isDashboard && !isAdmin && <Navbar />}
      <main className="flex-grow w-full">{children}</main>
      {!isDashboard && !isAdmin && <Footer />}
    </div>
  );
} 