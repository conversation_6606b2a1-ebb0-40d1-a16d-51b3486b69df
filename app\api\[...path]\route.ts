import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://fxentra-ec0dfccfb73c.herokuapp.com';

export async function GET(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'GET');
}

export async function POST(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'POST');
}

export async function PUT(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'PUT');
}

export async function DELETE(request: NextRequest, { params }: { params: { path: string[] } }) {
  return handleRequest(request, params, 'DELETE');
}

async function handleRequest(request: NextRequest, params: { path: string[] }, method: string) {
  try {
    // Get the API endpoint from header or construct from path
    const apiEndpoint = request.headers.get('X-API-Endpoint') || `${API_BASE_URL}/${params.path.join('/')}`;
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams.toString();
    const fullUrl = searchParams ? `${apiEndpoint}?${searchParams}` : apiEndpoint;

    // Prepare headers (exclude Next.js specific headers)
    const headers: Record<string, string> = {};
    request.headers.forEach((value, key) => {
      if (!key.startsWith('x-') && !key.startsWith('next-') && key !== 'host' && key !== 'X-API-Endpoint') {
        headers[key] = value;
      }
    });

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    };

    // Add body for POST/PUT requests
    if (method === 'POST' || method === 'PUT') {
      const contentType = request.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        requestOptions.body = JSON.stringify(await request.json());
      } else if (contentType?.includes('multipart/form-data')) {
        requestOptions.body = await request.formData();
        // Remove content-type header to let fetch set it with boundary
        delete headers['content-type'];
      } else {
        requestOptions.body = await request.text();
      }
    }

    // Make the actual API request
    const response = await fetch(fullUrl, requestOptions);
    
    // Get response data
    const responseData = await response.text();
    
    // Create response with original status and headers
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    });

    // Copy response headers
    response.headers.forEach((value, key) => {
      if (key !== 'content-encoding' && key !== 'content-length') {
        nextResponse.headers.set(key, value);
      }
    });

    return nextResponse;
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
