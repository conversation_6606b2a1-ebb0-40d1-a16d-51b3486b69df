// API Configuration
export const API_CONFIG = {
  // Base URLs
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://fxentra-ec0dfccfb73c.herokuapp.com',
  FALLBACK_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api',

  // Security settings - Enable in production or when explicitly set
  USE_PROXY: process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_USE_PROXY === 'true',
  MASK_URLS: process.env.NODE_ENV === 'production' || process.env.NEXT_PUBLIC_USE_PROXY === 'true',

  // Timeout settings
  REQUEST_TIMEOUT: 30000, // 30 seconds

  // Headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },

  // Endpoints
  ENDPOINTS: {
    // Auth endpoints
    LOGIN: 'auth/login',
    SIGNUP: 'auth/signup',
    VERIFY_EMAIL: 'auth/verify-email',
    RESEND_VERIFICATION: 'auth/resend-verification',
    USERS: 'auth/users',

    // Order endpoints
    ORDERS: 'order/orders',
    PENDING_ORDERS: 'order/pending_orders',
    COMPLETED_ORDERS: 'order/completed_orders',
    FAILED_ORDERS: 'order/failed_orders',
    PASSED_ORDERS: 'order/passed_orders',
    STAGE2_ORDERS: 'order/stage2_accounts',
    LIVE_ORDERS: 'order/live_accounts',
    RUNNING_ORDERS: 'order/running_orders',
    CERTIFICATES: 'order/certificates',
    PLACE_ORDER: 'order/order',
    ORDER_STATUS: 'order/order_status',
    ORDER_DETAILS: 'order/order_details',
    ORDER_IDS: 'order/order_ids',
    COMPLETE_ORDER: 'order/complete_order',
    EDIT_COMPLETE_ORDER: 'order/edit_complete_order',
    FAIL_ORDER: 'order/fail_order',
    PASS_ORDER: 'order/pass_order',
    EDIT_PASSED_ORDER: 'order/edit_passed_order',
    UPDATE_ORDER_STATUS: 'order/update_status',
    UPDATE_ORDER_DETAILS: 'order/update_details',

    // Account endpoints
    ACCOUNT_CREDENTIALS: 'account/credentials',
    PENDING_CONTAINERS: 'account/credentials/pending',
    ASSIGN_CONTAINER: 'account/credentials',
  },

  // Error messages
  ERROR_MESSAGES: {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    TIMEOUT_ERROR: 'Request timeout. Please try again.',
    UNAUTHORIZED: 'Session expired. Please login again.',
    FORBIDDEN: 'Access denied.',
    NOT_FOUND: 'Resource not found.',
    SERVER_ERROR: 'Server error. Please try again later.',
    UNKNOWN_ERROR: 'An unknown error occurred.',
  },
} as const;

// Helper function to get full endpoint URL
export const getEndpointUrl = (endpoint: keyof typeof API_CONFIG.ENDPOINTS): string => {
  return API_CONFIG.ENDPOINTS[endpoint];
};

// Helper function to get error message
export const getErrorMessage = (statusCode: number): string => {
  switch (statusCode) {
    case 401:
      return API_CONFIG.ERROR_MESSAGES.UNAUTHORIZED;
    case 403:
      return API_CONFIG.ERROR_MESSAGES.FORBIDDEN;
    case 404:
      return API_CONFIG.ERROR_MESSAGES.NOT_FOUND;
    case 500:
    case 502:
    case 503:
    case 504:
      return API_CONFIG.ERROR_MESSAGES.SERVER_ERROR;
    default:
      return API_CONFIG.ERROR_MESSAGES.UNKNOWN_ERROR;
  }
};
