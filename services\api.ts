import axios from 'axios';
import { API_CONFIG } from '@/config/api';

// Define API base URLs from configuration
const API_URL = API_CONFIG.FALLBACK_URL;
const API_BASE_URL = API_CONFIG.BASE_URL;

// Helper function to construct full API URLs
const getApiUrl = (endpoint: string): string => {
  // Remove leading slash if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  return `${API_BASE_URL}/${cleanEndpoint}`;
};

// Create a secure proxy function that masks URLs
const createSecureRequest = async (endpoint: string, options: any = {}) => {
  // Always use the proxy in browser environment when enabled
  if (typeof window !== 'undefined' && API_CONFIG.USE_PROXY) {
    // Clean the endpoint
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;

    // Create the proxy URL that will show in network tab
    const proxyUrl = `/api/${cleanEndpoint}`;

    // Make the request through the proxy
    return axios({
      url: proxyUrl,
      method: options.method || 'GET',
      data: options.data,
      params: options.params,
      headers: {
        ...options.headers,
        // Send the actual backend URL in a header for the proxy to use
        'X-API-Endpoint': `${API_BASE_URL}/${cleanEndpoint}`
      },
      timeout: API_CONFIG.REQUEST_TIMEOUT,
      withCredentials: false,
    });
  } else {
    // Direct call for server-side or when proxy is disabled
    const fullUrl = endpoint.startsWith('http') ? endpoint : getApiUrl(endpoint);
    return axios({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data,
      params: options.params,
      headers: options.headers,
      timeout: API_CONFIG.REQUEST_TIMEOUT,
      withCredentials: false,
    });
  }
};



// Helper function to check token expiration
const isTokenExpired = () => {
  const tokenExpiration = localStorage.getItem('tokenExpiration');
  if (!tokenExpiration) return true;

  const expiresAt = parseInt(tokenExpiration);
  const now = new Date().getTime();
  return now >= expiresAt;
};

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_URL,
  headers: API_CONFIG.DEFAULT_HEADERS,
  timeout: API_CONFIG.REQUEST_TIMEOUT,
  withCredentials: false, // Set to false for cross-origin requests to external APIs
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage if it exists
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

    // Check if token is expired
    if (token && isTokenExpired()) {
      // Clear auth data if token is expired
      if (typeof window !== 'undefined') {
        authService.logout();
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}&message=${encodeURIComponent('Your session has expired. Please login again.')}`;
        return Promise.reject('Token expired');
      }
    }

    // If token exists and is not expired, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle authentication errors (401)
    if (error.response && error.response.status === 401) {
      // Clear token and redirect to login if on client side
      if (typeof window !== 'undefined') {
        authService.logout();
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}&message=${encodeURIComponent('Your session has expired. Please login again.')}`;
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for direct API calls (bypassing baseURL)
export const directApiCall = async (url: string, options: any = {}) => {
  try {
    // Get token from localStorage if it exists
    const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

    // Check if token is expired
    if (token && isTokenExpired()) {
      authService.logout();
      if (typeof window !== 'undefined') {
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}&message=${encodeURIComponent('Your session has expired. Please login again.')}`;
      }
      throw new Error('Token expired');
    }



    // Check if data is FormData
    const isFormData = options.data instanceof FormData;

    // Set default headers
    const headers = {
      // Only set Content-Type if not FormData (axios will set it with boundary for FormData)
      ...(!isFormData && { 'Content-Type': 'application/json' }),
      'Accept': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...(options.headers || {})
    };

    // Use the secure request function that handles URL masking
    const response = await createSecureRequest(url, {
      ...options,
      headers
    });

    return response.data;
  } catch (error) {
    // Handle 401 errors
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      authService.logout();
      if (typeof window !== 'undefined') {
        window.location.href = `/login?redirect=${encodeURIComponent(window.location.pathname)}&message=${encodeURIComponent('Your session has expired. Please login again.')}`;
      }
    }
    throw error;
  }
};

// API service functions
export const authService = {
  login: async (username: string, password: string) => {
    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      const response = await directApiCall('auth/login', {
        method: 'POST',
        data: formData
      });

      if (response.access_token) {
        // Store token and user info with expiration
        const expiresIn = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
        const expiresAt = new Date().getTime() + expiresIn;

        localStorage.setItem('token', response.access_token);
        localStorage.setItem('tokenExpiration', expiresAt.toString());
        localStorage.setItem('username', username);
        localStorage.setItem('user_email', username);
      }

      return response;
    } catch (error) {
      throw error;
    }
  },

  signup: async (userData: {
    username: string;
    email: string;
    password: string;
    name: string;
    phone_no: string;
    country: string;
    address: string;
  }) => {
    try {
      const response = await directApiCall('auth/signup', {
        method: 'POST',
        data: userData
      });

      // Store email in localStorage for verification
      localStorage.setItem('user_email', userData.email);

      return response;
    } catch (error) {
      throw error;
    }
  },

  verifyEmail: async (email: string, code: string) => {
    try {
      const response = await directApiCall(`auth/verify-email?email=${encodeURIComponent(email)}&code=${code}`, {
        method: 'POST'
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  resendVerificationCode: async (email: string) => {
    try {
      const response = await directApiCall('auth/resend-verification', {
        method: 'POST',
        data: { email }
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('tokenExpiration');
    localStorage.removeItem('username');
    localStorage.removeItem('user_email');
    localStorage.removeItem('selectedOrderId');
  },

  isAuthenticated: () => {
    if (typeof window === 'undefined') return false;

    const token = localStorage.getItem('token');
    if (!token) return false;

    return !isTokenExpired();
  },

  getToken: () => {
    return typeof window !== 'undefined' ? localStorage.getItem('token') : null;
  },

  getUsername: () => {
    return typeof window !== 'undefined' ? localStorage.getItem('username') : null;
  }
};

export const challengeService = {
  getAllChallenges: async () => {
    const response = await apiClient.get('/challenges');
    return response.data;
  },

  getChallengeById: async (id: string) => {
    const response = await apiClient.get(`/challenges/${id}`);
    return response.data;
  },

  purchaseChallenge: async (challengeId: string, paymentData: any) => {
    const response = await apiClient.post(`/challenges/${challengeId}/purchase`, paymentData);
    return response.data;
  },
};

export const userDashboardService = {
  getAccountStats: async () => {
    const response = await apiClient.get('/dashboard/stats');
    return response.data;
  },

  getTradingHistory: async (filters: any = {}) => {
    const response = await apiClient.get('/dashboard/trading-history', { params: filters });
    return response.data;
  },

  getPayoutHistory: async () => {
    const response = await apiClient.get('/dashboard/payout-history');
    return response.data;
  },
};

// Default export for the base client
export default apiClient;