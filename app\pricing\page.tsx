import PricingSection from '@/components/PricingSection';

export default function PricingPage() {
  return (
    <div className="min-h-screen">
      <div className="bg-indigo-50 dark:bg-gray-800 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-extrabold tracking-tight text-gray-900 dark:text-white sm:text-5xl lg:text-6xl">
            Find the Perfect Challenge for Your Trading Style
          </h1>
          <p className="mt-6 text-xl text-gray-500 dark:text-gray-300 max-w-3xl mx-auto">
            Choose from our range of funding options designed to fit traders of all experience levels and capital requirements.
          </p>
        </div>
      </div>
      
      <PricingSection />
      
      {/* FAQ Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-base font-semibold text-indigo-600 dark:text-indigo-400 tracking-wide uppercase">Frequently Asked Questions</h2>
            <p className="mt-2 text-3xl font-extrabold text-gray-900 dark:text-white sm:text-4xl">
              Common Questions About Our Trading Challenges
            </p>
            <p className="mt-4 max-w-2xl text-xl text-gray-500 dark:text-gray-300 mx-auto">
              Find answers to the most common questions about our funding programs and challenges.
            </p>
          </div>
          
          <div className="mt-12 space-y-6 divide-y divide-gray-200 dark:divide-gray-700">
            {/* FAQ Item 1 */}
            <div className="pt-6">
              <dt className="text-lg">
                <span className="font-medium text-gray-900 dark:text-white">
                  How long does it take to complete a challenge?
                </span>
              </dt>
              <dd className="mt-2 text-base text-gray-500 dark:text-gray-300">
                Our challenges are designed to be completed within the specified timeframe for each tier - 10 days for Standard, 30 days for Professional, and 45 days for Elite challenges. However, you can complete them sooner if you reach your profit target while respecting the trading rules.
              </dd>
            </div>
            
            {/* FAQ Item 2 */}
            <div className="pt-6">
              <dt className="text-lg">
                <span className="font-medium text-gray-900 dark:text-white">
                  What happens if I don't reach the profit target?
                </span>
              </dt>
              <dd className="mt-2 text-base text-gray-500 dark:text-gray-300">
                If you don't reach the profit target within the specified timeframe but haven't violated any of the trading rules (such as maximum drawdown), you can apply for an extension for a small fee, or you can choose to restart the challenge with a discount.
              </dd>
            </div>
            
            {/* FAQ Item 3 */}
            <div className="pt-6">
              <dt className="text-lg">
                <span className="font-medium text-gray-900 dark:text-white">
                  What markets can I trade with my funded account?
                </span>
              </dt>
              <dd className="mt-2 text-base text-gray-500 dark:text-gray-300">
                We offer trading across a wide range of markets including forex pairs, cryptocurrencies, indices, stocks, and commodities. The specific instruments available depend on your account type and will be detailed once you've passed the challenge.
              </dd>
            </div>
            
            {/* FAQ Item 4 */}
            <div className="pt-6">
              <dt className="text-lg">
                <span className="font-medium text-gray-900 dark:text-white">
                  How often are profits paid out?
                </span>
              </dt>
              <dd className="mt-2 text-base text-gray-500 dark:text-gray-300">
                Profits are calculated and paid out on a bi-weekly basis once you've passed the challenge and are trading with a funded account. Payments are made via your preferred method including bank transfers, PayPal, or cryptocurrency.
              </dd>
            </div>
            
            {/* FAQ Item 5 */}
            <div className="pt-6">
              <dt className="text-lg">
                <span className="font-medium text-gray-900 dark:text-white">
                  Can I scale up my account size over time?
                </span>
              </dt>
              <dd className="mt-2 text-base text-gray-500 dark:text-gray-300">
                Yes, we offer a scaling program that allows traders to increase their account size by demonstrating consistent profitability. For every 10% profit you generate, you'll be eligible for a 25% increase in your account size, up to a maximum of $2 million.
              </dd>
            </div>
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              Still have questions? Contact our support team at{' '}
              <a href="mailto:<EMAIL>" className="text-indigo-600 dark:text-indigo-400 font-medium">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="bg-indigo-600 dark:bg-indigo-800">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between">
          <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
            <span className="block">Ready to start your funded journey?</span>
            <span className="block text-indigo-200">Choose your challenge and begin today.</span>
          </h2>
          <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
            <div className="inline-flex rounded-md shadow">
              <a
                href="/signup"
                className="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50"
              >
                Get started
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 