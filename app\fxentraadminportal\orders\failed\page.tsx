'use client';

import { useState, useEffect } from 'react';
import { Order, adminService } from '@/services/adminService';
import OrdersTable from '@/components/admin/OrdersTable';
import ViewOrderModal from '@/components/admin/ViewOrderModal';
import EditOrderModal from '@/components/admin/EditOrderModal';
import { directApiCall } from '@/services/api';

export default function FailedOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Fetch failed orders
  useEffect(() => {
    const fetchOrders = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await adminService.getFailedOrders();
        setOrders(data);
      } catch (error) {
        setError('Failed to load orders. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Handle order update
  const handleOrderUpdated = (updatedOrder: Order) => {
    // Update the orders list with the updated order
    setOrders(prevOrders =>
      prevOrders.map(order => order.id === updatedOrder.id ? updatedOrder : order)
    );
  };

  // Handle view order
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsViewModalOpen(true);
  };

  // Handle edit order
  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setIsEditModalOpen(true);
  };

  return (
    <div className="px-6 pb-6">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Failed Orders</h1>
          <p className="text-gray-400">View and manage failed orders</p>
        </div>
        <button
          onClick={() => {
            setIsLoading(true);
            setError(null);
            adminService.getFailedOrders()
              .then(data => {
                setOrders(data);
                setIsLoading(false);
              })
              .catch(() => {
                setError('Failed to refresh orders. Please try again.');
                setIsLoading(false);
              });
          }}
          disabled={isLoading}
          className="px-4 py-2 bg-teal-500/20 hover:bg-teal-500/30 rounded-md text-white flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          {isLoading ? 'Refreshing...' : 'Refresh Orders'}
        </button>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-teal-500/20 border-t-teal-500 rounded-full animate-spin"></div>
            <p className="mt-4 text-teal-500">Loading failed orders...</p>
          </div>
        </div>
      ) : error ? (
        <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 text-center">
          <p className="text-red-400">{error}</p>
          <button
            onClick={() => {
              setIsLoading(true);
              setError(null);
              adminService.getFailedOrders()
                .then(data => {
                  setOrders(data);
                  setIsLoading(false);
                })
                .catch(() => {
                  setError('Failed to load orders. Please try again later.');
                  setIsLoading(false);
                });
            }}
            className="mt-2 px-4 py-2 bg-red-500/20 hover:bg-red-500/30 rounded-md text-white transition-colors"
          >
            Retry
          </button>
        </div>
      ) : orders.length === 0 ? (
        <div className="bg-[#0F1A2E]/90 backdrop-blur-xl border border-teal-500/20 rounded-xl p-8 text-center">
          <svg className="w-16 h-16 mx-auto text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-xl font-medium text-white mb-2">No Failed Orders</h3>
          <p className="text-gray-400 mb-4">There are currently no failed orders to display.</p>
        </div>
      ) : (
        <OrdersTable
          orders={orders}
          title="Failed Orders"
          onOrderUpdated={handleOrderUpdated}
          onViewOrder={handleViewOrder}
          onEditOrder={handleEditOrder}
        />
      )}

      {/* View Order Modal */}
      <ViewOrderModal
        order={selectedOrder}
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        onEdit={handleEditOrder}
      />

      {/* Edit Order Modal */}
      <EditOrderModal
        order={selectedOrder}
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onOrderUpdated={handleOrderUpdated}
      />
    </div>
  );
}
