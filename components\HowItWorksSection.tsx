const steps = [
  {
    id: 1,
    title: 'Choose a Challenge',
    description: 'Select the account size and challenge that matches your trading goals and experience level.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
    ),
  },
  {
    id: 2,
    title: 'Pass the Challenge',
    description: 'Trade within our risk parameters and meet the profit target within the specified timeframe.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: 3,
    title: 'Get Funded',
    description: 'Once you pass, we\'ll provide you with a funded account where you can trade with our capital.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: 4,
    title: 'Earn Profits',
    description: 'Keep up to 90% of the profits you generate, with payouts available on a bi-weekly basis.',
    icon: (
      <svg className="h-10 w-10 text-teal-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7" />
      </svg>
    ),
  },
];

const HowItWorksSection = () => {
  return (
    <section id="howItWorks" className="py-24 bg-dark relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0 z-0">
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-gray-900 to-dark opacity-80"></div>
        
        {/* Premium geometric elements */}
        <div className="absolute top-0 left-0 w-full h-full opacity-5">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <linearGradient id="grid-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#4FD1C5" stopOpacity="0.5" />
                <stop offset="100%" stopColor="#2D3748" stopOpacity="0.3" />
              </linearGradient>
            </defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="url(#grid-gradient)" strokeWidth="0.5" />
            </pattern>
            <rect x="0" y="0" width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        
        {/* Glowing orbs */}
        <div className="absolute top-20 -left-20 w-60 h-60 rounded-full bg-gradient-to-r from-teal-400/10 to-blue-500/5 blur-3xl"></div>
        <div className="absolute bottom-20 -right-20 w-80 h-80 rounded-full bg-gradient-to-l from-teal-400/10 to-blue-500/5 blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          <h2 className="text-base font-semibold text-teal-400 tracking-wide uppercase drop-shadow-sm">HOW IT WORKS</h2>
          <p className="mt-3 text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl drop-shadow-md">
            Your Path to Funded Trading
          </p>
          <p className="mt-5 max-w-2xl text-xl text-gray-300 mx-auto">
            Our simple 4-step process takes you from challenge to funded trader with up to $200,000 in trading capital.
          </p>
        </div>

        <div className="mt-20">
          <div className="relative">
            {/* Connection line with gradient */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="hidden sm:block w-full h-0.5 bg-gradient-to-r from-transparent via-gray-700 to-transparent"></div>
            </div>
            
            {/* Steps */}
            <div className="relative flex justify-between items-center flex-col sm:flex-row">
              {steps.map((step, idx) => (
                <div 
                  key={step.id} 
                  className={`flex-1 text-center px-4 ${idx < steps.length - 1 ? 'mb-12 sm:mb-0' : ''}`}
                >
                  <div className="relative flex flex-col items-center group">
                    {/* Glowing effect behind icon */}
                    <div className="absolute inset-0 rounded-full bg-teal-400/10 blur-xl w-16 h-16 mx-auto"></div>
                    
                    {/* Icon with glass effect */}
                    <div className="rounded-full border-2 border-teal-400 bg-gray-800/80 backdrop-blur-sm p-4 relative z-10 shadow-lg shadow-teal-400/20 transition-all duration-300 group-hover:shadow-teal-400/30 group-hover:scale-105">
                      {step.icon}
                    </div>
                    
                    {/* Step number */}
                    <div className="absolute -top-3 -right-3 w-7 h-7 rounded-full bg-teal-400 flex items-center justify-center text-xs font-bold text-gray-900 z-20 shadow-md">
                      {step.id}
                    </div>
                    
                    <h3 className="mt-6 text-2xl font-bold text-white group-hover:text-teal-300 transition-colors duration-300">
                      {step.title}
                    </h3>
                    <p className="mt-3 text-base text-gray-300">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="mt-20 text-center">
          <p className="text-lg text-gray-300 mb-6">
            Ready to start your funded trading journey?
          </p>
          <a
            href="#pricing"
            className="inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-md text-gray-900 bg-gradient-to-r from-teal-400 to-teal-500 hover:from-teal-500 hover:to-teal-600 shadow-lg shadow-teal-400/20 hover:shadow-teal-400/30 transition-all duration-300 transform hover:-translate-y-1 md:text-lg"
          >
            View Challenges
          </a>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection; 