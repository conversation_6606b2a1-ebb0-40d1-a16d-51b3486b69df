# Phorm Trading - Frontend

This is the frontend application for Phorm Trading, a funded prop firm platform.

## Tech Stack

- Next.js 14 (App Router)
- TypeScript
- Tailwind CSS
- Axios for API calls

## Directory Structure

```
client/
├── app/            # Next.js App Router pages and layouts
├── components/     # Reusable UI components
├── lib/            # Utility functions and shared libraries
├── public/         # Static assets (images, fonts, etc.)
├── services/       # API services and external integrations
├── styles/         # Global styles (if needed beyond globals.css)
├── utils/          # Helper functions and utilities
```

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run the development server:
   ```bash
   npm run dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Building for Production

```bash
npm run build
```

## Environment Variables

Create a `.env.local` file in the client directory with the following variables:

```
# Backend API Configuration
NEXT_PUBLIC_API_BASE_URL=https://fxentra-ec0dfccfb73c.herokuapp.com

# Development API URL (fallback)
NEXT_PUBLIC_API_URL=http://localhost:5000/api

# Security Settings (automatically set based on NODE_ENV)
NODE_ENV=production
```

## Security Features

This application includes several security enhancements:

### 1. Environment-based Configuration
- All API endpoints are configured through environment variables
- No hardcoded URLs in the codebase
- Automatic fallback to development URLs when needed

### 2. URL Masking in Production
- In production, actual backend URLs are hidden from the browser's network tab
- Requests are proxied through Next.js API routes
- Network tab shows local domain instead of actual backend URLs

### 3. Console Log Removal
- All console.log, console.error, and console.warn statements have been removed
- No sensitive information is logged to the browser console
- Clean production build without debug information

### 4. Secure Request Handling
- Automatic token management and expiration checking
- Secure headers for all API requests
- Request timeout configuration
- Proper error handling without exposing internal details

## TODO

- Implement authentication (JWT/Supabase)
- Create protected trading dashboard
- Build admin panel
- Integrate payment processing (Stripe/Crypto)
- Add KYC verification