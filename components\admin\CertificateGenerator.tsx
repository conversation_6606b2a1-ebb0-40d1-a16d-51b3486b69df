'use client';

import { useState } from 'react';
import { Order } from '@/services/adminService';

interface CertificateGeneratorProps {
  order: Order;
  onClose: () => void;
}

export default function CertificateGenerator({ order, onClose }: CertificateGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [certificateData, setCertificateData] = useState({
    title: `Trading Certificate - ${order.account_size}`,
    traderName: order.username,
    accountId: order.id,
    accountSize: order.account_size,
    platform: order.platform,
    dateIssued: new Date().toLocaleDateString(),
    customMessage: 'Congratulations on successfully passing the trading challenge!'
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCertificateData(prev => ({ ...prev, [name]: value }));
  };

  const handleGenerate = () => {
    setIsGenerating(true);
    
    // Simulate certificate generation
    setTimeout(() => {
      setIsGenerating(false);
      alert(`Certificate for ${order.username} (Order ID: ${order.id}) has been generated and sent to their email.`);
      onClose();
    }, 2000);
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-black opacity-75"></div>
        </div>

        {/* Modal content */}
        <div className="inline-block align-bottom bg-[#0F1A2E] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-[#070F1B] px-6 py-4 border-b border-teal-500/20 flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Generate Certificate</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white focus:outline-none"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Body */}
          <div className="px-6 py-4">
            <div className="space-y-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-300 mb-1">
                  Certificate Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={certificateData.title}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                />
              </div>
              
              <div>
                <label htmlFor="traderName" className="block text-sm font-medium text-gray-300 mb-1">
                  Trader Name
                </label>
                <input
                  type="text"
                  id="traderName"
                  name="traderName"
                  value={certificateData.traderName}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="accountId" className="block text-sm font-medium text-gray-300 mb-1">
                    Account ID
                  </label>
                  <input
                    type="text"
                    id="accountId"
                    name="accountId"
                    value={certificateData.accountId}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="accountSize" className="block text-sm font-medium text-gray-300 mb-1">
                    Account Size
                  </label>
                  <input
                    type="text"
                    id="accountSize"
                    name="accountSize"
                    value={certificateData.accountSize}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="platform" className="block text-sm font-medium text-gray-300 mb-1">
                    Platform
                  </label>
                  <input
                    type="text"
                    id="platform"
                    name="platform"
                    value={certificateData.platform}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                  />
                </div>
                
                <div>
                  <label htmlFor="dateIssued" className="block text-sm font-medium text-gray-300 mb-1">
                    Date Issued
                  </label>
                  <input
                    type="text"
                    id="dateIssued"
                    name="dateIssued"
                    value={certificateData.dateIssued}
                    onChange={handleChange}
                    className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="customMessage" className="block text-sm font-medium text-gray-300 mb-1">
                  Custom Message
                </label>
                <textarea
                  id="customMessage"
                  name="customMessage"
                  rows={3}
                  value={certificateData.customMessage}
                  onChange={handleChange}
                  className="w-full bg-[#070F1B] border border-teal-500/20 rounded-md py-2 px-3 text-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-teal-500"
                />
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="bg-[#070F1B] px-6 py-4 border-t border-teal-500/20 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 mr-3"
            >
              Cancel
            </button>
            <button
              onClick={handleGenerate}
              disabled={isGenerating}
              className="px-4 py-2 bg-teal-600 text-white text-sm font-medium rounded-md hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 flex items-center"
            >
              {isGenerating ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Generating...
                </>
              ) : (
                'Generate Certificate'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
