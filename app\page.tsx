'use client';

import { useEffect } from 'react';
import HeroSection from '@/components/HeroSection';
import HowItWorksSection from '@/components/HowItWorksSection';
import PricingSection from '@/components/PricingSection';
import NewPricingSection from '@/components/NewPricingSection';
import FaqSection from '@/components/FaqSection';
import WhyUsSection from '@/components/WhyUsSection';

// Trust badges component
const TrustBadges = () => (
  <section className="py-16 relative overflow-hidden border-t border-gray-800">
    {/* Enhanced background with luxury patterns */}
    <div className="absolute inset-0 z-0">
      <div className="absolute inset-0 opacity-5 bg-grid-pattern"></div>
      <div className="absolute inset-0 bg-gradient-to-b from-black/30 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
    </div>
    
    {/* Premium glow effects */}
    <div className="absolute -top-40 -right-40 w-[500px] h-[500px] rounded-full bg-teal-600/5 blur-[100px]"></div>
    <div className="absolute -bottom-40 -left-40 w-[500px] h-[500px] rounded-full bg-teal-600/5 blur-[100px]"></div>
    
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div className="text-center mb-10">
        <div className="inline-flex items-center">
          <div className="h-[1px] w-12 bg-gradient-to-r from-transparent to-teal-400/50"></div>
          <p className="mx-4 text-xl text-gray-200 font-medium tracking-wide">
          Trusted by <span className="text-teal-400 font-bold">10,000+</span> traders worldwide
        </p>
          <div className="h-[1px] w-12 bg-gradient-to-l from-transparent to-teal-400/50"></div>
        </div>
      </div>
      
      {/* Metrics banner - premium design */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 sm:gap-8 lg:gap-10">
        <div className="group relative">
          {/* Card background with premium effect */}
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          
          {/* Subtle accent line */}
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          
          {/* Content */}
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">$758K+</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Capital Funded</p>
          </div>
        </div>
        
        <div className="group relative">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">98%</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Payout Rate</p>
          </div>
        </div>
        
        <div className="group relative">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">1.7K+</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Traders Funded</p>
          </div>
        </div>
        
        <div className="group relative">
          <div className="absolute inset-0 bg-gradient-to-b from-gray-800/50 to-gray-900/50 rounded-2xl border border-gray-800/50 transform transition-all duration-500 group-hover:border-teal-500/20 group-hover:shadow-[0_0_20px_rgba(20,184,166,0.1)]"></div>
          <div className="absolute top-0 left-[10%] right-[10%] h-[1px] bg-gradient-to-r from-transparent via-teal-500/20 to-transparent"></div>
          <div className="relative p-6 text-center">
            <div className="text-3xl sm:text-4xl lg:text-5xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-teal-400 to-teal-300 pb-1 mb-2">150+</div>
            <div className="h-[1px] w-12 mx-auto bg-gradient-to-r from-transparent via-teal-400/20 to-transparent mb-3"></div>
            <p className="text-sm text-gray-300 font-medium uppercase tracking-wider">Countries</p>
          </div>
        </div>
      </div>
    </div>
  </section>
);

export default function Home() {
  // Smooth scroll effect for anchor links
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isAnchor = target.tagName === 'A' && target.getAttribute('href')?.startsWith('#');
      
      if (isAnchor) {
        e.preventDefault();
        const href = target.getAttribute('href') as string;
        const element = document.querySelector(href);
        if (element) {
          window.scrollTo({
            top: element.getBoundingClientRect().top + window.pageYOffset - 80,
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleAnchorClick);
    return () => document.removeEventListener('click', handleAnchorClick);
  }, []);

  return (
    <main className="relative text-white font-sans selection:bg-teal-900/30 selection:text-teal-200">
      {/* Content */}
      <div className="relative z-10">
        {/* Hero Section */}
        <HeroSection />
        
        {/* Trust Badges */}
        <TrustBadges />
        
        {/* Why Us Section */}
        <WhyUsSection />
        
        {/* How It Works Section */}
        <HowItWorksSection />
        
        {/* Pricing Section */}
        <section id="pricing">
          <NewPricingSection />
        </section>
        
        {/* FAQ Section */}
        <section id="faq">
          <FaqSection />
        </section>
        
        {/* Final CTA Section */}
        <section id="footer" className="py-24 relative overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute inset-0 z-0 overflow-hidden">
            {/* Grid pattern */}
            <div 
              className="absolute inset-0 opacity-5" 
              style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
                backgroundSize: '30px 30px'
              }}
            />
            
            {/* Geometric shapes */}
            <div className="geometric-shape shape-teal-glow w-[600px] h-[600px] top-1/4 -left-[200px] opacity-20"></div>
            <div className="geometric-shape shape-teal-glow w-[500px] h-[500px] bottom-0 right-0 opacity-20"></div>
          </div>
          
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 text-center">
            <h2 className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-teal-400">
              Get Funded Today.<br className="hidden sm:block" /> Trade Like a Pro.
            </h2>
            
            <p className="mt-6 text-xl text-gray-300 max-w-3xl mx-auto">
              Start your funded trading journey with us today. Join thousands of traders who have turned their skills into consistent profits.
            </p>
            
            <div className="mt-10">
              <a
                href="/signup"
                className="inline-flex items-center justify-center px-10 py-5 border border-transparent text-lg font-semibold rounded-full text-white bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 shadow-lg shadow-teal-500/20 hover:shadow-xl hover:shadow-teal-500/30 transition-all duration-300 transform hover:-translate-y-1"
              >
                Get Funded
              </a>
            </div>
            
            <div className="mt-6">
              <p className="text-gray-400 text-sm">
                No time limits. No hidden fees. Start within minutes.
              </p>
            </div>
        </div>
      </section>
    </div>
    </main>
  );
} 