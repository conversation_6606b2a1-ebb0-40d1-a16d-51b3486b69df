@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset margins and paddings */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Site theme colors */
  --color-primary: 45, 212, 191; /* teal-400 */
  --color-primary-dark: 20, 184, 166; /* teal-500 */
  --color-dark: 5, 10, 16; /* #050A10 */
  --color-dark-light: 10, 14, 23; /* #0A0E17 */
  --color-dark-lighter: 19, 27, 41; /* #131B29 */
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

.bg-dark {
  background-color: #050A10;
}

/* Custom gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(to right, rgb(20, 184, 166), rgb(45, 212, 191));
}

.bg-gradient-secondary {
  background: linear-gradient(to right, #050A10, #0A0E17);
}

.bg-gradient-teal {
  background: linear-gradient(to bottom right, rgba(20, 184, 166, 0.8), rgba(45, 212, 191, 0.3));
}

/* Grid pattern for backgrounds */
.bg-grid-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  background-size: 30px 30px;
}

/* Decorative elements */
.geometric-shape {
  position: absolute;
  border-radius: 50%;
  z-index: 0;
  opacity: 0.1;
}

.shape-teal-glow {
  background: radial-gradient(circle, rgba(45, 212, 191, 0.15) 0%, rgba(17, 94, 89, 0.05) 50%, transparent 70%);
  animation: pulse 8s ease-in-out infinite alternate;
}

@keyframes pulse {
  0% {
    opacity: 0.05;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.08;
    transform: scale(1);
  }
}

/* Animation for slower pulse effect */
@keyframes pulse-slow {
  0% {
    opacity: 0.05;
    transform: scale(1);
  }
  50% {
    opacity: 0.1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.08;
    transform: scale(1);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 15s ease-in-out infinite alternate;
}

.animate-pulse-slow-delay {
  animation: pulse-slow 15s ease-in-out 2s infinite alternate;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Hide scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Custom scrollbar styling */
.custom-scrollbar {
  scrollbar-width: thin;  /* Firefox */
  scrollbar-color: rgba(45, 212, 191, 0.2) transparent;  /* Firefox */
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;  /* width of the entire scrollbar */
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;  /* color of the tracking area */
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(45, 212, 191, 0.2);  /* color of the scroll thumb */
  border-radius: 20px;  /* roundness of the scroll thumb */
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(45, 212, 191, 0.4);  /* color of the scroll thumb on hover */
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-md {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  }

  .text-shadow-lg {
    text-shadow: 0 15px 30px rgba(0, 0, 0, 0.11), 0 5px 15px rgba(0, 0, 0, 0.08);
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@keyframes slow-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-slow-spin {
  animation: slow-spin 20s linear infinite;
}

.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-gradient-shift {
  animation: gradient-shift 6s ease infinite;
  background-size: 400% 400%;
}

/* Modern components styling */
.card-dark {
  @apply bg-[#0A0E17] border border-gray-800 rounded-2xl shadow-xl relative overflow-hidden;
}

.button-primary {
  @apply bg-white text-gray-900 font-medium px-8 py-3 rounded-full flex items-center space-x-2 hover:bg-gray-100 transition-colors duration-300;
}

.button-secondary {
  @apply bg-[#131B29] text-white font-medium px-8 py-3 rounded-full flex items-center space-x-2 hover:bg-[#1c2539] transition-colors duration-300 border border-gray-800;
}

.input-dark {
  @apply bg-[#131B29] text-gray-300 border border-gray-800 rounded-xl p-4 focus:outline-none focus:ring-2 focus:ring-teal-500/50 w-full;
}

.section-heading {
  @apply text-3xl md:text-4xl font-bold text-white mb-3;
}

.section-subheading {
  @apply text-xl font-medium text-teal-400;
}