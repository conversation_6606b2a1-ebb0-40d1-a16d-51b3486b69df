'use client';

import { useEffect, useRef, useState } from 'react';

const features = [
  {
    title: "No Time Limits",
    description: "Trade at your own pace without the stress of time constraints. Our challenges focus on sustainable trading, not rushing to hit targets.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12H9" />
      </svg>
    ),
    tag: "Flexibility",
    gradient: "from-cyan-500/20 to-teal-500/5"
  },
  {
    title: "Realistic Trading Conditions",
    description: "Trade in real market conditions with direct access to liquidity providers, tight spreads, and ultra-fast execution.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>
    ),
    tag: "Accuracy",
    gradient: "from-blue-500/20 to-teal-500/5"
  },
  {
    title: "Fast Withdrawals",
    description: "Request your profits and receive them within 24 hours. No delays, no excuses - just quick and efficient payouts.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
    tag: "Payouts",
    gradient: "from-emerald-500/20 to-teal-500/5"
  },
  {
    title: "Advanced Trading Analytics",
    description: "Access powerful trading analytics and performance insights to refine your strategy and maximize your profitability.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    tag: "Insights",
    gradient: "from-violet-500/20 to-indigo-500/5"
  },
  {
    title: "Trade Any Instrument",
    description: "Forex, indices, commodities, crypto - trade what you know best with no restrictions on your preferred market.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
      </svg>
    ),
    tag: "Versatility",
    gradient: "from-amber-500/20 to-orange-500/5"
  },
  {
    title: "Responsive Support",
    description: "24/7 customer support from a team of professional traders. Get answers quickly from people who understand trading.",
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>
    ),
    tag: "Assistance",
    gradient: "from-pink-500/20 to-purple-500/5"
  },
];

const WhyUsSection = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [hoveredFeature, setHoveredFeature] = useState<number | null>(null);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => {
      if (sectionRef.current) {
        observer.disconnect();
      }
    };
  }, []);

  return (
    <section 
      ref={sectionRef}
      className="py-24 bg-[#050A10] relative"
    >
      {/* Background pattern */}
      <div className="absolute inset-0 z-0 opacity-5">
        <div 
          className="absolute inset-0" 
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'40\' height=\'40\' viewBox=\'0 0 40 40\'%3E%3Cg fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.4\'%3E%3Cpath d=\'M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 20.83l2.83-2.83 1.41 1.41L1.41 22H0v-1.17zM0 3.07l2.83-2.83 1.41 1.41L1.41 4.24H0V3.07zm33.66 30.1l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zm-14.66 0l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zM1.41 22l2.83-2.83 1.41 1.41L2.83 23.41 1.41 22zm32.25-18.24l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zm-14.66 0l-2.83 2.83-1.41-1.41 2.83-2.83 1.41 1.41zM1.41 4.24l2.83-2.83 1.41 1.41L2.83 5.65 1.41 4.24z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '20px 20px'
          }}
        />
      </div>

      {/* Gradient background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-1/4 -right-1/4 w-[800px] h-[800px] bg-gradient-to-tr from-teal-500/5 via-blue-500/5 to-transparent rounded-full blur-3xl opacity-30"></div>
        <div className="absolute -bottom-1/4 left-1/4 w-[600px] h-[600px] bg-gradient-to-br from-blue-500/5 via-teal-500/5 to-transparent rounded-full blur-3xl opacity-30"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <span className="inline-block px-3 py-1 bg-teal-500/10 text-teal-400 rounded-full text-sm font-medium mb-4">Premium Benefits</span>
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">
            Why <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-teal-500">Choose Us</span>
          </h2>
          <p className="text-gray-300 max-w-2xl mx-auto text-lg">
            We've built our platform with traders in mind, focusing on what truly matters for your success.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className={`bg-[#0D131F] border border-gray-800 p-7 rounded-xl hover:border-teal-500/30 hover:shadow-lg transition-all duration-300 transform ${
                isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
              }`}
              style={{ transitionDelay: `${index * 100}ms` }}
              onMouseEnter={() => setHoveredFeature(index)}
              onMouseLeave={() => setHoveredFeature(null)}
            >
              {/* Feature tag */}
              <div className="flex justify-between items-start mb-5">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${feature.gradient} flex items-center justify-center text-teal-400 transition-all duration-300 ${
                  hoveredFeature === index ? 'scale-110 shadow-md shadow-teal-500/10' : ''
                }`}>
                  {feature.icon}
                </div>
                <span className="inline-block px-2.5 py-1 bg-[#1A202C]/80 text-teal-400/90 text-xs font-medium rounded-full">
                  {feature.tag}
                </span>
              </div>

              <h3 className={`text-xl font-bold mb-3 transition-colors duration-300 ${
                hoveredFeature === index ? 'text-teal-300' : 'text-white'
              }`}>
                {feature.title}
              </h3>
              
              <p className="text-gray-400 leading-relaxed">{feature.description}</p>
              
              {/* Visual accent element */}
              <div className={`mt-6 h-1 w-12 bg-gradient-to-r from-teal-500 to-teal-300 rounded-full transition-all duration-300 ${
                hoveredFeature === index ? 'w-24' : 'w-12'
              }`}></div>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="mt-16 text-center">
          <a
            href="/signup"
            className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-semibold rounded-lg shadow-lg shadow-teal-500/20 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl hover:shadow-teal-500/30"
          >
            Get Started Today
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </a>
          <p className="mt-4 text-gray-500 text-sm">Join 30,000+ funded traders worldwide</p>
        </div>
      </div>
    </section>
  );
};

export default WhyUsSection; 