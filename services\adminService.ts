import { directApiCall } from './api';

// Types
export interface TimelineEvent {
  event_type: string;
  event_date: string;
  notes: string;
}

export interface StageAccount {
  server: string;
  platform_login: string;
  platform_password: string;
  session_id: string;
  terminal_id: number;
  profit_target?: number;
  profit_share?: number;
  status: string;
  created_at: string;
}

export interface Order {
  id: string | number;
  order_id?: string;
  username: string;
  email: string;
  challenge_type: string;
  account_size: string;
  platform: string;
  payment_method?: string;
  txid?: string;
  status: OrderStatus | string;
  created_at: string | null;
  updated_at?: string | null;
  completed_at?: string | null;
  image?: {
    url?: string;
    image_url?: string;
    created_at: string;
  };
  timeline?: TimelineEvent[];
  // Additional fields for trading account details
  server?: string;
  platform_login?: string;
  platform_password?: string;
  profit_target?: number;
  session_id?: string;
  terminal_id?: number;
  drawdown?: number;
  container_id?: number;
  // Status flags
  completed?: boolean;
  passed?: boolean;
  failed?: boolean;
  is_active?: boolean;
  is_stage2?: boolean;
  stage2_account?: StageAccount;
  is_live?: boolean;
  live_account?: StageAccount;
  complete_order_id?: number;
  // Failed order specific fields
  fail_order_id?: number;
  reason?: string;
  // Passed order specific fields
  pass_order_id?: number;
  pass_date?: string;
  profit_amount?: number;
  notes?: string;
  // Stage 2 specific fields
  stage2_id?: number;
  // Live account specific fields
  live_account_id?: number;
  // Running order specific fields
  is_running?: boolean;
}

export interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  country: string;
  phone_no: string;
  address: string;
  hashed_password: string;
  is_verified: boolean | string | number;
}

export interface Certificate {
  certificate_number: string;
  order_id: string;
  username: string;
  issue_date: string;
  account_size: string;
  challenge_type: string;
  profit_target: number;
}

export type OrderStatus =
  | 'incomplete'
  | 'completed'
  | 'failed'
  | 'stage_2'
  | 'live'
  | 'running'
  | 'active'
  | 'inactive'
  | 'passed';

export interface OrderSummary {
  total: number;
  completed: number;
  failed: number;
  passed: number;
  stage_2: number;
  live: number;
  running: number;
  incomplete: number;
  certificates: number;
}

export interface AdminDashboardSummary {
  totalOrders: number;
  totalUsers: number;
  orderSummary: OrderSummary;
}

export interface ContainerAccount {
  id: number;
  platform: string;
  server: string;
  platform_login: string;
  platform_password: string;
  account_size: string;
  account_type: string;
  is_assigned: boolean;
  order_id: number | null;
  created_at: string;
  updated_at: string | null;
  status: string;
}

// Admin service functions
export const adminService = {
  // Get all orders
  getAllOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get pending orders
  getPendingOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/pending_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get completed orders
  getCompletedOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/completed_orders');
      return data;
    } catch (error) {
      return [];
    }
  },

  // Get failed orders
  getFailedOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/failed_orders');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the failed orders data to the Order interface
      return data.map((failedOrder: any) => ({
        id: failedOrder.fail_order_id || failedOrder.order_id,
        order_id: failedOrder.order_id,
        username: failedOrder.username,
        email: failedOrder.email,
        challenge_type: failedOrder.challenge_type,
        account_size: failedOrder.account_size,
        platform: failedOrder.platform,
        payment_method: failedOrder.payment_method,
        txid: failedOrder.txid,
        status: 'failed',
        created_at: failedOrder.created_at || new Date().toISOString(),
        server: failedOrder.server,
        platform_login: failedOrder.platform_login,
        platform_password: failedOrder.platform_password,
        image: failedOrder.image,
        failed: true,
        reason: failedOrder.reason
      }));
    } catch (error) {
      return [];
    }
  },

  // Get passed orders
  getPassedOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/passed_orders');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the passed orders data to the Order interface
      return data.map((passedOrder: any) => ({
        id: passedOrder.pass_order_id || passedOrder.order_id,
        order_id: passedOrder.order_id,
        username: passedOrder.username,
        email: passedOrder.email,
        challenge_type: passedOrder.challenge_type,
        account_size: passedOrder.account_size,
        platform: passedOrder.platform,
        status: 'passed',
        created_at: passedOrder.pass_date || new Date().toISOString(),
        passed: true,
        profit_amount: passedOrder.profit_amount,
        notes: passedOrder.notes
      }));
    } catch (error) {
      return [];
    }
  },

  // Get stage 2 orders
  getStage2Orders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/stage2_accounts');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the stage 2 orders data to the Order interface
      return data.map((stage2Order: any) => ({
        id: stage2Order.id || stage2Order.order_id,
        order_id: stage2Order.order_id,
        username: stage2Order.username,
        email: stage2Order.email,
        challenge_type: stage2Order.challenge_type,
        account_size: stage2Order.account_size,
        platform: stage2Order.platform,
        status: stage2Order.status || 'stage_2',
        created_at: stage2Order.created_at || new Date().toISOString(),
        server: stage2Order.server,
        platform_login: stage2Order.platform_login,
        platform_password: stage2Order.platform_password,
        session_id: stage2Order.session_id,
        terminal_id: stage2Order.terminal_id,
        profit_target: stage2Order.profit_target,
        is_stage2: true,
        stage2_id: stage2Order.id
      }));
    } catch (error) {
      return [];
    }
  },

  // Get live orders
  getLiveOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/live_accounts');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the live orders data to the Order interface
      return data.map((liveOrder: any) => ({
        id: liveOrder.live_account_id || liveOrder.order_id,
        order_id: liveOrder.order_id,
        username: liveOrder.username,
        email: liveOrder.email,
        challenge_type: liveOrder.challenge_type,
        account_size: liveOrder.account_size,
        platform: liveOrder.platform,
        status: 'live',
        created_at: liveOrder.created_at || new Date().toISOString(),
        is_live: true,
        live_account_id: liveOrder.live_account_id
      }));
    } catch (error) {
      return [];
    }
  },

  // Get running orders
  getRunningOrders: async (): Promise<Order[]> => {
    try {
      const data = await directApiCall('order/running_orders');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      // Map the running orders data to the Order interface
      return data.map((runningOrder: any) => ({
        id: runningOrder.id || runningOrder.order_id,
        order_id: runningOrder.order_id,
        username: runningOrder.username,
        email: runningOrder.email,
        challenge_type: runningOrder.challenge_type,
        account_size: runningOrder.account_size,
        platform: runningOrder.platform,
        payment_method: runningOrder.payment_method,
        txid: runningOrder.txid,
        status: runningOrder.status || 'running',
        created_at: runningOrder.created_at || new Date().toISOString(),
        completed_at: runningOrder.completed_at,
        image: runningOrder.image,
        timeline: runningOrder.timeline,
        server: runningOrder.server,
        platform_login: runningOrder.platform_login,
        platform_password: runningOrder.platform_password,
        session_id: runningOrder.session_id,
        terminal_id: runningOrder.terminal_id,
        profit_target: runningOrder.profit_target,
        completed: runningOrder.completed,
        passed: runningOrder.passed,
        failed: runningOrder.failed,
        is_active: runningOrder.is_active,
        is_stage2: runningOrder.is_stage2,
        is_live: runningOrder.is_live,
        is_running: true
      }));
    } catch (error) {
      return [];
    }
  },

  // Get all users
  getAllUsers: async (): Promise<User[]> => {
    try {
      const data = await directApiCall('auth/users');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      return data;
    } catch (error) {
      return [];
    }
  },

  // Get certificates
  getCertificates: async (): Promise<Certificate[]> => {
    try {
      const data = await directApiCall('order/certificates');

      // Ensure we return an array even if the API returns a single object
      if (data && !Array.isArray(data)) {
        return [data];
      }

      // Return empty array if data is null or undefined
      if (!data) {
        return [];
      }

      return data;
    } catch (error) {
      return [];
    }
  },

  // Get dashboard summary - we'll calculate this from the other endpoints
  getDashboardSummary: async (): Promise<AdminDashboardSummary> => {
    try {
      const [allOrders, allUsers] = await Promise.all([
        adminService.getAllOrders(),
        adminService.getAllUsers()
      ]);

      const completedOrders = await adminService.getCompletedOrders();
      const failedOrders = await adminService.getFailedOrders();
      const passedOrders = await adminService.getPassedOrders();
      const stage2Orders = await adminService.getStage2Orders();
      const liveOrders = await adminService.getLiveOrders();
      const runningOrders = await adminService.getRunningOrders();
      const certificates = await adminService.getCertificates();

      return {
        totalOrders: allOrders.length,
        totalUsers: allUsers.length,
        orderSummary: {
          total: allOrders.length,
          completed: completedOrders.length,
          failed: failedOrders.length,
          passed: passedOrders.length,
          stage_2: stage2Orders.length,
          live: liveOrders.length,
          running: runningOrders.length,
          certificates: certificates.length,
          incomplete: allOrders.length - (
            completedOrders.length + failedOrders.length + passedOrders.length +
            stage2Orders.length + liveOrders.length + runningOrders.length
          )
        }
      };
    } catch (error) {
      // Return default values if there's an error
      return {
        totalOrders: 0,
        totalUsers: 0,
        orderSummary: {
          total: 0,
          completed: 0,
          failed: 0,
          passed: 0,
          stage_2: 0,
          live: 0,
          running: 0,
          certificates: 0,
          incomplete: 0
        }
      };
    }
  },

  // Complete an order
  completeOrder: async (orderId: string, orderDetails: {
    platform_login: string;
    platform_password: string;
    server: string;
    profit_target?: number;
    session_id?: string;
    terminal_id?: number;
  }): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();
      Object.entries(orderDetails).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const data = await directApiCall(`order/complete_order/${orderId}`, {
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Edit a completed order
  editCompletedOrder: async (orderId: string, orderDetails: {
    platform_login: string;
    platform_password: string;
    server: string;
    profit_target?: number;
    session_id?: string;
    terminal_id?: number;
  }): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();
      Object.entries(orderDetails).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const data = await directApiCall(`order/edit_complete_order/${orderId}`, {
        method: 'PUT',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Fail an order
  failOrder: async (orderId: string, failDetails?: {
    reason?: string;
  }): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();

      // Add fail details if provided
      if (failDetails) {
        Object.entries(failDetails).forEach(([key, value]) => {
          if (value !== undefined) {
            formData.append(key, value.toString());
          }
        });
      }

      const data = await directApiCall(`order/fail_order/${orderId}`, {
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Pass an order
  passOrder: async (orderId: string, passDetails?: {
    profit_amount?: number;
    notes?: string;
  }): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();

      // Add pass details if provided
      if (passDetails) {
        Object.entries(passDetails).forEach(([key, value]) => {
          if (value !== undefined) {
            formData.append(key, value.toString());
          }
        });
      }

      const data = await directApiCall(`order/pass_order/${orderId}`, {
        method: 'POST',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Edit a passed order
  editPassedOrder: async (orderId: string, passedOrderDetails: {
    platform_login: string;
    platform_password: string;
    server: string;
    terminal_id: number;
    session_id: string;
    profit_target: number;
    account_type: 'stage2' | 'live';
  }): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();
      Object.entries(passedOrderDetails).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const data = await directApiCall(`order/edit_passed_order/${orderId}`, {
        method: 'PUT',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Update order status (legacy method - use specific methods above when possible)
  updateOrderStatus: async (orderId: string, status: OrderStatus): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();
      formData.append('status', status);

      const data = await directApiCall(`order/update_status/${orderId}`, {
        method: 'PUT',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  // Update order details (legacy method - use specific methods above when possible)
  updateOrderDetails: async (orderId: string, orderDetails: {
    platform_login?: string;
    platform_password?: string;
    server?: string;
    profit_target?: number;
    session_id?: string;
    terminal_id?: number;
    drawdown?: number;
    container_id?: number;
  }): Promise<Order> => {
    try {
      // Create FormData
      const formData = new FormData();
      Object.entries(orderDetails).forEach(([key, value]) => {
        if (value !== undefined) {
          formData.append(key, value.toString());
        }
      });

      const data = await directApiCall(`order/update_details/${orderId}`, {
        method: 'PUT',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  async getContainers(): Promise<ContainerAccount[]> {
    try {
      const data = await directApiCall('account/credentials');
      return data;
    } catch (error) {
      throw error;
    }
  },

  async createContainer(containerData: {
    platform: string;
    server: string;
    platform_login: string;
    platform_password: string;
    account_size: string;
    account_type: string;
  }): Promise<ContainerAccount> {
    try {
      const data = await directApiCall('account/credentials', {
        method: 'POST',
        data: {
          credentials: [containerData]
        }
      });
      return data;
    } catch (error) {
      throw error;
    }
  },

  async getPendingContainers(): Promise<ContainerAccount[]> {
    try {
      const data = await directApiCall('account/credentials/pending');
      return data;
    } catch (error) {
      throw error;
    }
  },

  async assignContainerToOrder(credentialId: number, orderId: string | number, profitTarget: number): Promise<ContainerAccount> {
    try {
      const data = await directApiCall(`account/credentials/${credentialId}/assign/${orderId}?profit_target=${profitTarget}`, {
        method: 'PUT'
      });
      return data;
    } catch (error) {
      throw error;
    }
  },
};

export default adminService;
